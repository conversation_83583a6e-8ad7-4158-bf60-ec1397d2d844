# FAAC API Integration Documentation

## Overview

This document describes the integration of the FAAC (Federal Account Allocation Committee) API for the Gross VAT Allocation and Net VAT Allocation by States chart in the BrewLens dashboard.

## Features Implemented

### 1. API Service (`src/services/faacApi.js`)
- **faacAuthFetch**: Enhanced fetch function with client credentials and token refresh
- **getFaacStateData**: Fetch paginated FAAC state data
- **getAllFaacStateData**: Fetch all data with automatic pagination
- **transformFaacDataForCharts**: Transform API data to match chart requirements

### 2. Custom Hook (`src/hooks/useFaacData.js`)
- **useFaacData**: Basic data fetching with caching and error handling
- **useFaacChartData**: Enhanced hook with filtering capabilities

### 3. Authentication Updates (`src/store/authStore.js`)
- Added `clientId` and `clientSecret` storage
- Updated auth methods to handle client credentials
- Default values set to "string" as per current login implementation

### 4. Dashboard Integration (`src/components/BrewLens/NewFaacDashboard.jsx`)
- Toggle between API data and local data
- Real-time API status indicators
- Loading states and error handling
- Data source badges
- Refresh functionality

## API Endpoint Details

**Endpoint**: `/api/databrew/faac/state/`
**Method**: GET
**Headers Required**:
- `Authorization: Bearer {access_token}`
- `client-id: CL_cbffb2401ebc4889SO94e1d8451a3c8c5c`
- `client-secret: 84430783858eac2f4f0f6270fa80161bb208795c787d126de6889685075b8c88`

**Query Parameters**:
- `page` (required): Page number
- `page_size` (optional): Number of records per page (default: 12)

**Note**: This API will be sold as a service to other clients, hence the client credentials requirement.

## Usage

### 1. Basic Usage
The dashboard automatically attempts to use API data when:
- User is authenticated (has access token)
- Client credentials are available
- API toggle is enabled (default)

### 2. Manual Control
Users can:
- Toggle between "API Data" and "Local Data" using the button in the header
- Refresh API data using the refresh button
- View data source status via badges and alerts

### 3. Error Handling
- Automatic fallback to local data on API errors
- Clear error messages displayed to users
- Retry functionality available

## Data Transformation

The API data is transformed to match the expected chart format:

```javascript
{
  beneficiaries: "State Name",
  month: "January",
  year: 2022,
  gross_vat_allocation: 1234567890,
  net_vat_allocation: 987654321,
  statutory_allocation: 555666777,
  exchange_gain_allocation: 111222333,
  total_net_amount: 2000000000,
  net_share_of_ecology: 123456789,
  // ... other fields
}
```

## Configuration

### Environment Variables
Add these to your `.env` file:

```env
# API Base URL
NEXT_PUBLIC_API_BASE=http://*************:9000

# FAAC API Credentials
NEXT_PUBLIC_FAAC_CLIENT_ID=CL_cbffb2401ebc4889SO94e1d8451a3c8c5c
NEXT_PUBLIC_FAAC_CLIENT_SECRET=84430783858eac2f4f0f6270fa80161bb208795c787d126de6889685075b8c88
```

### Client Credentials
Using production credentials from environment variables:
- `NEXT_PUBLIC_FAAC_CLIENT_ID`: "CL_cbffb2401ebc4889SO94e1d8451a3c8c5c"
- `NEXT_PUBLIC_FAAC_CLIENT_SECRET`: "84430783858eac2f4f0f6270fa80161bb208795c787d126de6889685075b8c88"

These are stored in the `.env` file and loaded via environment variables for better security and configuration management.

## Testing

### Browser Console Testing
1. Open browser console
2. Navigate to the FAAC dashboard
3. Run: `window.testFaacIntegration()`

### Manual Testing
1. Ensure you're logged in
2. Navigate to the FAAC dashboard
3. Check the data source badge (should show "API Data" if successful)
4. Try toggling between API and local data
5. Test the refresh functionality

## Troubleshooting

### Common Issues

1. **"No access token available"**
   - Solution: Log in to the application first

2. **"Failed to load API data"**
   - Check network connectivity
   - Verify API endpoint is accessible
   - Check client credentials are correct

3. **"API request failed: 422"**
   - Validation error - check required parameters
   - Verify client-id and client-secret headers

4. **Data not displaying**
   - Check browser console for errors
   - Verify data transformation is working
   - Try toggling to local data to isolate the issue

### Debug Mode
Enable debug logging by opening browser console and setting:
```javascript
localStorage.setItem('debug', 'faac:*');
```

## Future Enhancements

1. **Client Credential Management**
   - UI for managing client credentials
   - Environment-specific credentials

2. **Advanced Filtering**
   - Server-side filtering via API parameters
   - Date range filtering

3. **Caching Improvements**
   - Local storage caching
   - Cache invalidation strategies

4. **Performance Optimization**
   - Lazy loading for large datasets
   - Virtual scrolling for tables

## Security Considerations

- Client credentials are stored in browser storage (consider security implications)
- Access tokens are automatically refreshed
- API calls include proper authentication headers
- Error messages don't expose sensitive information
