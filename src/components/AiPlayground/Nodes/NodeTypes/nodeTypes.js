import {
  CodeNode,
  ComparatorNode,
  DiscordNode,
  EmailNode,
  FacebookNode,
  FileNode,
  GoogleDriveUploadNode,
  GoogleDriveDownloadNode,
  HttpRequestNode,
  ModelNode,
  NotionNode,
  PostgreSQLNode,
  PromptNode,
  S3DownloadNode,
  S3UploadNode,
  SlackNode,
  TelegramInputNode,
  TelegramOutputNode,
  UrlScanNode,
  WhatsAppInputNode,
  WhatsAppOutputNode,
  XNode,
} from "..";

export const nodeType = {
  prompt: PromptNode,
  model: ModelNode,
  file: FileNode,
  x: XNode,
  email: EmailNode,
  "whatsapp-input": WhatsAppInputNode,
  "whatsapp-output": WhatsAppOutputNode, // Reuse for output
  "s3-upload": S3UploadNode,
  "s3-download": S3DownloadNode,
  discord: DiscordNode,
  "telegram-output": TelegramOutputNode,
  "telegram-input": TelegramInputNode,
  slack: <PERSON><PERSON>ck<PERSON><PERSON>,
  notion: NotionN<PERSON>,
  code: <PERSON><PERSON><PERSON>,
  facebook: FacebookN<PERSON>,
  "gdrive-upload": GoogleDriveUploadNode,
  "gdrive-download": GoogleDriveDownloadNode,
  comparator: ComparatorNode,
  postgresql: PostgreSQLNode,
  http: HttpRequestNode,
  urlscan: UrlScanNode,
};
