"use client";
import { Command, Play, Trash2 } from "lucide-react";
import { useTheme } from "next-themes";
import { useState } from "react";
import { Handle, Position, useReactFlow } from "@xyflow/react";
import { useTerminalStore } from "@/store/useTerminalStore";
import { useXNodeStore } from "@/store/nodes/XNodeStore/useXNodeStore";
import useNodeDataStore from "@/store/nodes/useNodeDataStore";
import Image from "next/image";
import XConfig from "../../NodeConfigs/XConfig/XConfig";

function XNode({ data, id }) {
  const { theme } = useTheme();
  const { updateNodeData, deleteElements } = useReactFlow();
  const { addLog } = useTerminalStore();
  const useStore = useXNodeStore(id);
  const { action, setResponseData, validate, commit, reset, formatForBackend } =
    useStore();
  const [showXConfig, setShowXConfig] = useState(false);

  // useEffect(() => {
  //   reset(data);
  // }, [data, reset]);

  const mockApiCall = (apiData) => {
    const { options, username, max_results } = apiData;
    if (options === "scrape") {
      return {
        posts: [
          {
            id: "123",
            text: `Sample post from ${username || "unknown"}`,
            username: username || "@user",
            date: "2025-05-14",
          },
        ].slice(0, max_results),
      };
    } else {
      return {
        username: username || "@user",
        followers: 1000,
        bio: "Sample bio",
      };
    }
  };

  const handlePlay = () => {
    const { isValid, errors } = validate();
    if (!isValid) {
      addLog({
        type: "error",
        message: `XNode ${id} validation failed: ${JSON.stringify(errors)}`,
      });
      return;
    }

    const configData = commit();
    const json = formatForBackend();
    const response = mockApiCall(json);
    setResponseData(response);

    // Store in useNodeDataStore for ModelNode's data handle
    useNodeDataStore.getState().updateNodeData(id, {
      type: "x",
      payload: { ...configData.payload, responseData: response },
    });

    // Update React Flow node data for XConfig
    updateNodeData(id, configData);

    addLog({
      type: "success",
      message: `Processed XNode ${id} (${action}) with response: ${JSON.stringify(
        response
      )}`,
    });
  };

  return (
    <div
      className={`${
        theme === "dark" ? "bg-[#2F4F4F]" : "bg-gray-100"
      } border-[5px] border-[#D3D3D3] rounded-3xl shadow min-w-[400px] max-w-[550px] px-7 py-7 `}
    >
      <div className="  pb-8 ">
        <div className="flex gap-3 items-center">
          <Image src={"/x.svg"} width={70} height={70} alt="x-icon" />
          {/* <span className="text-3xl font-semibold">X</span> */}
        </div>
      </div>

      <div className="flex justify-between items-center pt-7">
        <button onClick={() => setShowXConfig(!showXConfig)}>
          <Command className="w-11 h-11" />
        </button>
        <div className="flex gap-4 items-center">
          <Play className="w-11 h-11 cursor-pointer" onClick={handlePlay} />

          <button
            className=" p-1 rounded-sm cursor-pointer"
            onClick={() => deleteElements({ nodes: [{ id }] })}
          >
            <Trash2 className="w-11 h-11" />
          </button>
        </div>
      </div>

      <XConfig open={showXConfig} onOpenChange={setShowXConfig} nodeId={id} />
      <Handle
        style={{ backgroundColor: "blue", width: 20, height: 20 }}
        type="source"
        position={Position.Right}
        id="data"
      />
    </div>
  );
}

export default XNode;
