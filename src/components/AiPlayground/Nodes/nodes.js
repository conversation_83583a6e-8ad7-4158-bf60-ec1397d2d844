const nodes = [
  {
    type: "whatsapp",
    label: "WhatsApp",
    icon: "/whatsapp-3.svg",
    color: "#25D366",
    actions: [
      {
        type: "whatsapp-input",
        label: "WhatsApp - on get message",
        icon: "/whatsapp-3.svg",
        color: "#25D366",
        config: {
          accountSid: "",
          authToken: "",
          toNumber: "",
          message: "",
        },
      },
      {
        type: "whatsapp-output",
        label: "WhatsApp - send message",
        icon: "/whatsapp-3.svg",
        color: "#25D366",
        config: {
          accountSid: "",
          authToken: "",
          fromNumber: "",
          toNumbers: [],
          message: "",
        },
      },
    ],
  },
  {
    type: "s3",
    label: "Amazon S3",
    icon: "/s3-Service.svg",
    color: "#FF9900",
    actions: [
      {
        type: "s3-upload",
        label: "s3 - Upload",
        icon: "/s3-Service.svg",
        color: "#25D366",
        config: {
          accessKeyId: "",
          secretAccessKey: "",
          bucketName: "",
          objectKey: "",
          acl: "private",
          fileContent: "",
        },
      },
      {
        type: "s3-download",
        label: "s3 - Download/View",
        icon: "/s3-Service.svg",
        color: "#25D366",
        config: {
          accessKeyId: "",
          secretAccessKey: "",
          bucketName: "",
          objectKey: "",
          acl: "private",
          fileContent: "",
        },
      },
    ],
  },
  {
    type: "slack",
    label: "Slack ",
    icon: "/slack.svg",
    color: "#FF9900",
    actions: [],
    config: {
      botToken: "",
      channelId: "",
      teamId: "",
      message: "",
      action: "send_message",
      messageFormat: "text",
      attachmentUrl: "",
      attachmentTitle: "",
      interactiveBlocks: "",
      threadTs: "",
      username: "",
      iconEmoji: "",
      iconUrl: "",
      unfurlLinks: true,
      unfurlMedia: true,
    },
  },
  {
    type: "notion",
    label: "Notion",
    icon: "/notion.svg",
    color: "#FF9900",
    actions: [],
    config: {
      apiToken: "",
      databaseId: "",
      pageId: "",
      action: "create_page",
      title: "",
      content: "",
      propertiesMapping: "",
      parentType: "database",
      coverUrl: "",
      iconEmoji: "",
      iconUrl: "",
      archived: false,
    },
  },
  {
    type: "discord",
    label: "Discord",
    icon: "/discord-2.svg",
    color: "#FF9900",
    actions: [],
    config: {
      botToken: "",
      channelId: "",
      message: "",
      embedJson: "",
      action: "send_message",
      username: "",
      avatarUrl: "",
      tts: false,
    },
  },
  {
    type: "googledrive",
    label: "Google Drive",
    icon: "/gdrive.svg",
    color: "#4285F4",
    actions: [
      {
        type: "gdrive-upload",
        label: "Google Drive - Upload",
        icon: "/gdrive.svg",
        color: "#25D366",
        config: {
          clientId: "",
          clientSecret: "",
          refreshToken: "",
          accessToken: "",
          folderId: "",
          fileName: "",
          fileContent: "",
          fileUrl: "",
          mimeType: "",
          action: "upload_file",
          fileType: "text",
          permissions: "",
          description: "",
        },
      },
      {
        type: "gdrive-download",
        label: "Google Drive - Download",
        icon: "/gdrive.svg",
        color: "#25D366",
        config: {
          clientId: "",
          clientSecret: "",
          refreshToken: "",
          accessToken: "",
          folderId: "",
          fileName: "",
          fileContent: "",
          fileUrl: "",
          mimeType: "",
          action: "upload_file",
          fileType: "text",
          permissions: "",
          description: "",
        },
      },
    ],
  },
  {
    type: "code",
    label: "Code",
    icon: "/code.svg",
    color: "#6B7280",
    actions: [],
    config: {
      language: "javascript",
      codeSnippet: "",
      inputFields: "",
      outputFields: "",
      timeout: 30,
      environment: "node",
      packages: "",
      inputData: "",
      outputData: "",
    },
  },
  {
    type: "comparator",
    label: "Comparator",
    icon: "/comparator.svg",
    color: "#6B7280",
    actions: [],
    config: {
      valueA: "",
      valueB: "",
      operator: "==",
      dataType: "text",
      precision: 2,
    },
  },
  {
    type: "model",
    label: "Model",
    icon: "/model.svg",
    color: "#10B981",
    actions: [],
    config: {
      modelName: "OpenAI (ChatGPT)",
      version: 1,
      apiKey: "",
      query: "",
      data: {},
    },
  },
  {
    type: "prompt",
    label: "Prompt",
    icon: "/prompt.svg",
    color: "#10B981",
    actions: [],
    config: {
      query: "",
    },
  },
  {
    type: "http",
    label: "HTTP Request",
    icon: "/httprequest.svg",
    color: "#3B82F6",
    actions: [],
    config: {
      method: "GET",
      url: "",
      headers: [],
      body: "",
      bodyFormat: "none",
      authType: "none",
      authCredentials: {
        username: "",
        password: "",
        token: "",
      },
    },
  },
  {
    type: "postgresql",
    label: "PostgreSQL",
    icon: "/postgresql.svg",
    color: "#3B82F6",
    actions: [],
    config: {
      host: "",
      port: "",
      database: "",
      user: "",
      password: "",
      sqlStatement: "",
      parameters: [],
    },
  },
  {
    type: "urlscan",
    label: "urlscan.io",
    icon: "/urlscan.svg",
    color: "#3B82F6",
    actions: [],
    config: {
      apiKey: "",
      url: "",
      waitForComplete: false,
      viewRawResults: false,
    },
  },
  {
    type: "telegram",
    label: "Telegram",
    icon: "/telegram-1.svg",
    color: "#25D366",
    actions: [
      {
        type: "telegram-output",
        label: "Telegram - send message",
        icon: "/telegram-1.svg",
        color: "#25D366",
        config: {
          botToken: "",
          chatId: "",
          message: "",
          action: "send_message",
          parseMode: "Markdown",
          fileUrl: "",
          fileName: "",
          disableWebPagePreview: false,
          disableNotification: false,
          replyToMessageId: "",
        },
      },
      {
        type: "telegram-input",
        label: "Telegram - on get message",
        icon: "/telegram-1.svg",
        color: "#25D366",
        config: {
          botToken: "",
        },
      },
    ],
  },
];

export default nodes;
