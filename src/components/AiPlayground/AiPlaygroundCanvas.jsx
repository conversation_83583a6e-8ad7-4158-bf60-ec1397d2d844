// "use client";

// import {
//   ReactFlow,
//   Controls,
//   Background,
//   addEdge,
//   useNodesState,
//   useEdgesState,
//   ReactFlowProvider,
//   useReactFlow,
// } from "@xyflow/react";
// import "@xyflow/react/dist/style.css";
// import { SidebarTrigger, useSidebar } from "../ui/sidebar";
// import { useCallback, useEffect, useRef, useState } from "react";
// import { DnDProvider, useDnD } from "@/context/DnDContext";
// import CanvasDropdown from "./CanvasDropdown";
// import {
//   ModelNode,
//   PromptNode,
//   FileNode,
//   XNode,
//   EmailNode,
//   WhatsAppNode,
//   DiscordNode,
//   S3Node,
//   TelegramNode,
//   SlackNode,
//   CodeNode,
//   NotionNode,
//   FacebookNode,
//   GoogleDriveNode,
//   ComparatorNode,
//   PostgreSQLNode,
//   HttpRequestNode,
//   UrlScanNode,
// } from "./Nodes";

// import { useTheme } from "next-themes";
// import { WorkflowToaster } from "./Toasters/WorkflowToaster";
// import useWorkflowStore from "@/store/workflowStore";
// import UnsavedChangesModal from "./Modals/UnsavedChangesModal";
// import { useRouter } from "next/navigation";
// import { PanelLeft } from "lucide-react";
// import { useTerminalStore } from "@/store/useTerminalStore";

// const nodeTypes = {
//   prompt: PromptNode,
//   model: ModelNode,
//   file: FileNode,
//   x: XNode,
//   email: EmailNode,
//   whatsapp: WhatsAppNode,
//   s3: S3Node,
//   discord: DiscordNode,
//   telegram: TelegramNode,
//   slack: SlackNode,
//   notion: NotionNode,
//   code: CodeNode,
//   facebook: FacebookNode,
//   googledrive: GoogleDriveNode,
//   comparator: ComparatorNode,
//   postgres: PostgreSQLNode,
//   http: HttpRequestNode,
//   urlscan: UrlScanNode,
// };
// const initialNodes = [];
// const initialEdges = [];

// let id = 1;
// const getId = () => `node_${id++}`;

// function AiPlaygroundCanvas() {
//   const { state } = useSidebar();
//   const sidebarWidth = state === "collapsed" ? "3rem" : "16rem";
//   const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
//   const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
//   const { screenToFlowPosition, toObject } = useReactFlow();
//   const [type] = useDnD();
//   const { currentWorkflowId, currentWorkflowName, workflows } =
//     useWorkflowStore();
//   const [lastSavedWorkflow, setLastSavedWorkflow] = useState(null);
//   const router = useRouter();
//   const [showUnsavedModal, setShowUnsavedModal] = useState(false);
//   const [nextRoute, setNextRoute] = useState(null);
//   const hasUnsavedChangesRef = useRef(null);
//   const runAllNodesRef = useRef(null);
//   const updateLastSavedWorkflowRef = useRef(null);
//   const autoSaveRef = useRef(null);
//   const handleSaveRef = useRef(null);
//   const { isTerminalVisible } = useTerminalStore();

//   const onConnect = useCallback(
//     (connection) => setEdges((eds) => addEdge(connection, eds)),
//     [setEdges]
//   );

//   const onDragOver = useCallback((event) => {
//     event.preventDefault();
//     event.dataTransfer.dropEffect = "move";
//   }, []);

//   const onDrop = useCallback(
//     (event) => {
//       event.preventDefault();
//       if (!type) {
//         return;
//       }

//       const position = screenToFlowPosition({
//         x: event.clientX,
//         y: event.clientY,
//       });

//       const initialData = JSON.parse(
//         event.dataTransfer.getData("application/json") || "{}"
//       );

//       const newNode = {
//         id: getId(),
//         type: type.toLowerCase(),
//         position,
//         data: { ...initialData },
//         draggable: true,
//       };

//       setNodes((nds) => nds.concat(newNode));
//     },
//     [type, setNodes, screenToFlowPosition]
//   );

//   const hasUnsavedChanges = useCallback(() => {
//     const workflow = toObject();
//     const nodeCount = workflow.nodes.length;
//     const edgeCount = workflow.edges.length;

//     if (nodeCount === 0 && edgeCount === 0) {
//       return false;
//     }

//     if (!lastSavedWorkflow) {
//       return true;
//     }

//     return (
//       JSON.stringify(workflow.nodes) !==
//         JSON.stringify(lastSavedWorkflow.nodes) ||
//       JSON.stringify(workflow.edges) !== JSON.stringify(lastSavedWorkflow.edges)
//     );
//   }, [toObject, lastSavedWorkflow]);

//   const runAllNodes = useCallback(async () => {
//     console.log("Running all nodes:", {
//       nodes: nodes.length,
//       edges: edges.length,
//     });
//     for (const node of nodes) {
//       const nodeComponent = nodeTypes[node.type];
//       if (nodeComponent && nodeComponent.prototype.handlePlay) {
//         try {
//           await nodeComponent.prototype.handlePlay.call({
//             id: node.id,
//             data: node.data,
//           });
//           console.log(`Node ${node.id} (${node.type}) executed successfully`);
//         } catch (error) {
//           console.error(`Error running node ${node.id} (${node.type}):`, error);
//         }
//       }
//     }
//   }, [nodes, edges]);

//   useEffect(() => {
//     hasUnsavedChangesRef.current = hasUnsavedChanges;
//     runAllNodesRef.current = runAllNodes;
//     updateLastSavedWorkflowRef.current = (workflow) =>
//       setLastSavedWorkflow(workflow);
//   }, [hasUnsavedChanges, runAllNodes]);

//   const { theme } = useTheme();
//   const [bgColor, setBgColor] = useState("");
//   const [bgSize, setBgSize] = useState("");
//   const [bgGap, setBgGap] = useState(0);

//   useEffect(() => {
//     setBgColor(theme === "dark" ? "#333333" : "");
//     setBgSize(theme === "dark" ? "6" : "3");
//     setBgGap(theme === "dark" ? 34 : 40);
//   }, [theme]);

//   useEffect(() => {
//     const handleRouteChangeStart = () => {
//       if (hasUnsavedChangesRef.current && hasUnsavedChangesRef.current()) {
//         setNextRoute(window.location.pathname);
//         setShowUnsavedModal(true);
//         history.pushState(null, null, window.location.href); // Prevent navigation
//         return false;
//       }
//       return true;
//     };

//     const handleBeforeUnload = (event) => {
//       if (hasUnsavedChangesRef.current && hasUnsavedChangesRef.current()) {
//         event.preventDefault();
//         event.returnValue =
//           "You have unsaved changes. Are you sure you want to leave?";
//       }
//     };

//     window.addEventListener("popstate", handleRouteChangeStart);
//     window.addEventListener("beforeunload", handleBeforeUnload);

//     return () => {
//       window.removeEventListener("popstate", handleRouteChangeStart);
//       window.removeEventListener("beforeunload", handleBeforeUnload);
//     };
//   }, [router]);

//   const handleSave = () => {
//     if (autoSaveRef.current) {
//       autoSaveRef.current(toObject());
//     }
//   };

//   const handleDiscard = () => {
//     if (nextRoute) {
//       router.push(nextRoute);
//     }
//   };

//   return (
//     <WorkflowToaster>
//       <div
//         className={` ${isTerminalVisible ? "h-[70%]" : "h-[100%]"} relative`}
//       >
//         <div
//           className="fixed top-0 left-0 h-12 group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-14 shrink-0 items-center gap-2  z-40 transition-all duration-300 ease-in-out pt-3"
//           style={{
//             width: `calc(100dvw - ${sidebarWidth})`,
//             marginLeft: sidebarWidth,
//           }}
//         >
//           <div className="flex items-center justify-between px-4 ">
//             {/* <Header /> */}
//             <SidebarTrigger suppressHydrationWarning>
//               <PanelLeft className="h-[1.2rem] w-[1.2rem]" />
//               <span className="sr-only">Toggle Sidebar</span>
//             </SidebarTrigger>

//             <CanvasDropdown
//               onAutoSave={autoSaveRef}
//               hasUnsavedChangesRef={hasUnsavedChangesRef}
//               runAllNodesRef={runAllNodesRef}
//               updateLastSavedWorkflowRef={updateLastSavedWorkflowRef}
//               handleSaveRef={handleSaveRef}
//               existingNames={workflows.map((w) => w.name.toLowerCase())}
//             />
//           </div>
//         </div>

//         <div
//           className={` h-[100%] relative`}
//           style={{ width: `calc(100dvw - ${sidebarWidth})` }}
//         >
//           <div className="h-[100%] relative">
//             <ReactFlow
//               nodes={nodes}
//               edges={edges}
//               onNodesChange={onNodesChange}
//               onEdgesChange={onEdgesChange}
//               onConnect={onConnect}
//               onDragOver={onDragOver}
//               onDrop={onDrop}
//               nodeTypes={nodeTypes}
//               zoomOnScroll={false}
//               zoomOnDoubleClick={false}
//               defaultViewport={{ x: 0, y: 0, zoom: 0.4 }}
//             >
//               <Controls style={{ color: "black" }} orientation="horizontal" />
//               <Background gap={bgGap} size={bgSize} color={bgColor} />
//             </ReactFlow>
//           </div>
//           <UnsavedChangesModal
//             isOpen={showUnsavedModal}
//             onOpenChange={setShowUnsavedModal}
//             onSave={handleSave}
//             onDiscard={handleDiscard}
//             isNewWorkflow={
//               !currentWorkflowId || currentWorkflowName === "preview"
//             }
//             handleSave={handleSaveRef.current}
//             existingNames={workflows.map((w) => w.name.toLowerCase())}
//           />
//         </div>
//       </div>
//     </WorkflowToaster>
//   );
// }

// export default () => (
//   <ReactFlowProvider>
//     <DnDProvider>
//       <AiPlaygroundCanvas />
//     </DnDProvider>
//   </ReactFlowProvider>
// );

// AiPlaygroundCanvas.jsx
"use client";

import {
  ReactFlow,
  Controls,
  Background,
  addEdge,
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
  useReactFlow,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { SidebarTrigger, useSidebar } from "../ui/sidebar";
import { useCallback, useEffect, useRef, useState } from "react";
import { DnDProvider, useDnD } from "@/context/DnDContext";
import SidebarContainer from "./SidebarContainer";

import { useTheme } from "next-themes";
import { WorkflowToaster } from "./Toasters/WorkflowToaster";
import useWorkflowStore from "@/store/workflowStore";
import UnsavedChangesModal from "./Modals/UnsavedChangesModal";
import { useRouter } from "next/navigation";
import { PanelLeft } from "lucide-react";
import { useTerminalStore } from "@/store/useTerminalStore";
import { nodeType } from "./Nodes/NodeTypes/nodeTypes";

const nodeTypes = nodeType;

const initialNodes = [];
const initialEdges = [];

let id = 1;
const getId = () => `node_${id++}`;

function AiPlaygroundCanvas() {
  const { state } = useSidebar();
  const sidebarWidth = state === "collapsed" ? "3rem" : "16rem";
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const { screenToFlowPosition, toObject } = useReactFlow();
  const [type] = useDnD();
  const { currentWorkflowId, currentWorkflowName, workflows } =
    useWorkflowStore();
  const [lastSavedWorkflow, setLastSavedWorkflow] = useState(null);
  const router = useRouter();
  const [showUnsavedModal, setShowUnsavedModal] = useState(false);
  const [nextRoute, setNextRoute] = useState(null);
  const hasUnsavedChangesRef = useRef(null);
  const runAllNodesRef = useRef(null);
  const updateLastSavedWorkflowRef = useRef(null);
  const autoSaveRef = useRef(null);
  const handleSaveRef = useRef(null);
  const { isTerminalVisible } = useTerminalStore();

  const onConnect = useCallback(
    (connection) => setEdges((eds) => addEdge(connection, eds)),
    [setEdges]
  );

  const onDragOver = useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = useCallback(
    (event) => {
      event.preventDefault();
      if (!type || !nodeTypes[type]) {
        console.error(`Node type ${type} not found in nodeTypes`);
        return;
      }

      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      const initialData = JSON.parse(
        event.dataTransfer.getData("application/json") || "{}"
      );

      const newNode = {
        id: getId(),
        type: type,
        position,
        data: { ...initialData },
        draggable: true,
      };

      setNodes((nds) => nds.concat(newNode));
    },
    [type, setNodes, screenToFlowPosition]
  );

  const hasUnsavedChanges = useCallback(() => {
    const workflow = toObject();
    const nodeCount = workflow.nodes.length;
    const edgeCount = workflow.edges.length;

    if (nodeCount === 0 && edgeCount === 0) {
      return false;
    }

    if (!lastSavedWorkflow) {
      return true;
    }

    return (
      JSON.stringify(workflow.nodes) !==
        JSON.stringify(lastSavedWorkflow.nodes) ||
      JSON.stringify(workflow.edges) !== JSON.stringify(lastSavedWorkflow.edges)
    );
  }, [toObject, lastSavedWorkflow]);

  const runAllNodes = useCallback(async () => {
    console.log("Running all nodes:", {
      nodes: nodes.length,
      edges: edges.length,
    });
    for (const node of nodes) {
      const nodeComponent = nodeTypes[node.type];
      if (nodeComponent && nodeComponent.prototype.handlePlay) {
        try {
          await nodeComponent.prototype.handlePlay.call({
            id: node.id,
            data: node.data,
          });
          console.log(`Node ${node.id} (${node.type}) executed successfully`);
        } catch (error) {
          console.error(`Error running node ${node.id} (${node.type}):`, error);
        }
      }
    }
  }, [nodes, edges]);

  useEffect(() => {
    hasUnsavedChangesRef.current = hasUnsavedChanges;
    runAllNodesRef.current = runAllNodes;
    updateLastSavedWorkflowRef.current = (workflow) =>
      setLastSavedWorkflow(workflow);
  }, [hasUnsavedChanges, runAllNodes]);

  const { theme } = useTheme();
  const [bgColor, setBgColor] = useState("");
  const [bgSize, setBgSize] = useState("");
  const [bgGap, setBgGap] = useState(0);

  useEffect(() => {
    setBgColor(theme === "dark" ? "#333333" : "");
    setBgSize(theme === "dark" ? "6" : "3");
    setBgGap(theme === "dark" ? 34 : 40);
  }, [theme]);

  useEffect(() => {
    const handleRouteChangeStart = () => {
      if (hasUnsavedChangesRef.current && hasUnsavedChangesRef.current()) {
        setNextRoute(window.location.pathname);
        setShowUnsavedModal(true);
        history.pushState(null, null, window.location.href);
        return false;
      }
      return true;
    };

    const handleBeforeUnload = (event) => {
      if (hasUnsavedChangesRef.current && hasUnsavedChangesRef.current()) {
        event.preventDefault();
        event.returnValue =
          "You have unsaved changes. Are you sure you want to leave?";
      }
    };

    window.addEventListener("popstate", handleRouteChangeStart);
    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("popstate", handleRouteChangeStart);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [router]);

  const handleSave = () => {
    if (autoSaveRef.current) {
      autoSaveRef.current(toObject());
    }
  };

  const handleDiscard = () => {
    if (nextRoute) {
      router.push(nextRoute);
    }
  };

  return (
    <WorkflowToaster>
      <div className={`${isTerminalVisible ? "h-[70%]" : "h-[100%]"} relative`}>
        <div
          className="fixed top-0 left-0 h-12 group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-14 shrink-0 items-center gap-2 z-40 transition-all duration-300 ease-in-out pt-3"
          style={{
            width: `calc(100dvw - ${sidebarWidth})`,
            marginLeft: sidebarWidth,
          }}
        >
          <div className="flex items-center justify-between px-4">
            <SidebarTrigger suppressHydrationWarning>
              <PanelLeft className="h-[1.2rem] w-[1.2rem]" />
              <span className="sr-only">Toggle Sidebar</span>
            </SidebarTrigger>
            <SidebarContainer
              onAutoSave={autoSaveRef}
              hasUnsavedChangesRef={hasUnsavedChangesRef}
              runAllNodesRef={runAllNodesRef}
              updateLastSavedWorkflowRef={updateLastSavedWorkflowRef}
              handleSaveRef={handleSaveRef}
              existingNames={workflows.map((w) => w.name.toLowerCase())}
            />
          </div>
        </div>
        <div
          className={`h-[100%] relative`}
          style={{ width: `calc(100dvw - ${sidebarWidth})` }}
        >
          <div className="h-[100%] relative">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onDragOver={onDragOver}
              onDrop={onDrop}
              nodeTypes={nodeTypes}
              zoomOnScroll={false}
              zoomOnDoubleClick={false}
              defaultViewport={{ x: 0, y: 0, zoom: 0.4 }}
            >
              <Controls style={{ color: "black" }} orientation="horizontal" />
              <Background gap={bgGap} size={bgSize} color={bgColor} />
            </ReactFlow>
          </div>
          <UnsavedChangesModal
            isOpen={showUnsavedModal}
            onOpenChange={setShowUnsavedModal}
            onSave={handleSave}
            onDiscard={handleDiscard}
            isNewWorkflow={
              !currentWorkflowId || currentWorkflowName === "preview"
            }
            handleSave={handleSaveRef.current}
            existingNames={workflows.map((w) => w.name.toLowerCase())}
          />
        </div>
      </div>
    </WorkflowToaster>
  );
}

export default () => (
  <ReactFlowProvider>
    <DnDProvider>
      <AiPlaygroundCanvas />
    </DnDProvider>
  </ReactFlowProvider>
);
