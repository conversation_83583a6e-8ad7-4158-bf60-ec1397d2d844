"use client";

import { ArrowLeft } from "lucide-react";
import nodes from "./Nodes/nodes";
import NodeItem from "./NodeItem";

function ActionNodeList({ activeNode, setActiveNode, searchTerm }) {
  const selectedNode = nodes.find((node) => node.type === activeNode);
  const filterText = searchTerm.toLowerCase();

  if (!activeNode || !selectedNode) {
    return null;
  }

  return (
    <div
      className={`absolute top-0 left-0 w-full h-full flex flex-col gap-1 pb-9 transition-transform duration-300 ease-in-out z-50  bg-[#18181b] overflow-y-auto ${
        activeNode ? "translate-x-0" : "translate-x-full"
      }`}
      style={{ scrollbarWidth: "thin", scrollbarColor: "#64748b #1e293b" }}
    >
      <div className="flex items-center gap-2 p-2">
        <button
          onClick={() => setActiveNode(null)}
          className="p-1 hover:bg-muted rounded-md transition-colors"
        >
          <ArrowLeft className="w-5 h-5 text-muted-foreground" />
        </button>
        <span className="text-sm font-semibold">
          {selectedNode.label} Actions
        </span>
      </div>
      {selectedNode.actions
        .filter((action) => action.label.toLowerCase().includes(filterText))
        .map((action) => (
          <NodeItem key={action.type} node={action} />
        ))}
    </div>
  );
}

export default ActionNodeList;
