"use client"

import React, { useState } from 'react';
import { Search, Send, Sparkles } from 'lucide-react';
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";


const QueryRAGSection = ({ onQuery }) => {
  const [query, setQuery] = useState('');
  const [isQuerying, setIsQuerying] = useState(false);

  const handleSubmit = async () => {
    if (!query.trim()) return;
    
    setIsQuerying(true);
    try {
      await onQuery(query);
    } finally {
      setIsQuerying(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const exampleQueries = [
    "How do I reset my password?",
    "What are the API rate limits?",
    "How to configure OAuth authentication?",
    "What's the database backup schedule?"
  ];

  return (
    <Card className="rag-container animate-fade-in">
      <CardHeader className="pb-6">
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent flex items-center gap-2">
          <Search className="w-6 h-6 text-primary" />
          Query RAG
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          Ask questions about your vectorized data and get intelligent responses
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-6">
          <div className="rag-card">
            <div className="space-y-4">
              <div>
                <Label htmlFor="rag-query" className="text-sm font-medium text-foreground flex items-center gap-2">
                  <Sparkles className="w-4 h-4 text-primary" />
                  Your Question
                </Label>
                <Textarea
                  id="rag-query"
                  placeholder="Ask anything about your data..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="rag-input mt-2 min-h-[120px] resize-none"
                  disabled={isQuerying}
                />
                <p className="text-xs text-muted-foreground mt-2">
                  Press Cmd/Ctrl + Enter to submit
                </p>
              </div>

              <Button 
                onClick={handleSubmit}
                className="rag-button w-full flex items-center gap-2"
                disabled={!query.trim() || isQuerying}
              >
                {isQuerying ? (
                  <>
                    <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                    Processing Query...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4" />
                    Submit Query
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Example Queries */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-muted-foreground">Example Queries</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {exampleQueries.map((example, index) => (
                <button
                  key={index}
                  onClick={() => setQuery(example)}
                  className="rag-button-secondary text-left p-3 hover:scale-[1.02] transition-transform"
                  disabled={isQuerying}
                >
                  <div className="flex items-start gap-2">
                    <Search className="w-4 h-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{example}</span>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QueryRAGSection;