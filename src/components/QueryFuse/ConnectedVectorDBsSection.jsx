import React from 'react';
import { Database, RefreshCw, MessageCircle, FileText, Settings, Unlink } from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";



const ConnectedVectorDBsSection = ({
  databases,
  onDisconnect,
  onVectorize,
  onQueryChat,
  onQueryGeneration,
  onAPIDetails
}) => {
  const getStatusBadge = (status) => {
    const statusMap = {
      connected: 'status-connected',
      disconnected: 'status-disconnected',
      processing: 'status-processing'
    };

    const statusText = {
      connected: 'Connected',
      disconnected: 'Disconnected',
      processing: 'Processing'
    };

    return (
      <span className={`status-badge ${statusMap[status]}`}>
        {statusText[status]}
      </span>
    );
  };

  const controlButtons = [
    {
      id: 'vectorize',
      icon: RefreshCw,
      tooltip: 'Vectorize Data',
      onClick: onVectorize,
      variant: 'default'
    },
    {
      id: 'chat',
      icon: MessageCircle,
      tooltip: 'Query in Chat Mode',
      onClick: onQueryChat,
      variant: 'default'
    },
    {
      id: 'generation',
      icon: FileText,
      tooltip: 'Query in Generation Mode',
      onClick: onQueryGeneration,
      variant: 'default'
    },
    {
      id: 'api',
      icon: Settings,
      tooltip: 'API Details',
      onClick: onAPIDetails,
      variant: 'default'
    }
  ];

  return (
    <Card className="rag-container animate-fade-in">
      <CardHeader className="pb-6">
        <CardTitle className="text-2xl font-bold text-foreground flex items-center gap-2">
          <Database className="w-6 h-6 text-primary" />
          Connected Vector DBs
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          Manage your connected vector databases and their operations
        </CardDescription>
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {databases.length === 0 ? (
            <div className="rag-card text-center py-12">
              <Database className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No Vector Databases Connected</h3>
              <p className="text-muted-foreground">
                Connect your first data source above to get started with RAG
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {databases.map((db) => (
                <div key={db.id} className="rag-card p-4 rounded-lg hover:shadow-xl transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Database className="w-5 h-5 text-primary" />
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-1">
                          <h3 className="text-lg font-semibold text-foreground truncate">
                            {db.type} - {db.name}
                          </h3>
                          {getStatusBadge(db.status)}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Last sync: {db.lastSync}</span>
                          <span>•</span>
                          <span>{db.documentCount.toLocaleString()} documents</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <TooltipProvider>
                        {/* Control buttons */}
                        <div className="flex items-center space-x-2">
                          {controlButtons.map((button) => (
                            <Tooltip key={button.id}>
                              <TooltipTrigger asChild>
                                <button
                                  onClick={() => button.onClick(db.id)}
                                  className="rag-icon-button"
                                  disabled={db.status === 'processing'}
                                >
                                  <button.icon className="w-4 h-4" />
                                </button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{button.tooltip}</p>
                              </TooltipContent>
                            </Tooltip>
                          ))}
                        </div>

                        {/* Disconnect button */}
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              onClick={() => onDisconnect(db.id)}
                              variant="destructive"
                              size="sm"
                              className="hover:scale-110 transition-transform"
                              disabled={db.status === 'processing'}
                            >
                              <Unlink className="w-4 h-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Disconnect Database</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>

                  {/* Processing indicator */}
                  {db.status === 'processing' && (
                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                        <span>Processing vectorization...</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ConnectedVectorDBsSection;