"use client";

import { PlusCircle, AlertTriangle, Shield, CheckCircle } from "lucide-react";
import { NavMain } from "@/components/SideBar/nav-main";
import { NavUser } from "@/components/SideBar/nav-user";

import {
  <PERSON>bar,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import Link from "next/link";

import Image from "next/image";
import { NavDataBrew } from "@/components/SideBar/nav-dataBrew";
import { NavWorkFlows } from "@/components/SideBar/nav-workflows";
import { NavMlAnalytics } from "@/components/SideBar/nav-mlAnalytics";
import { NavMarketPlace } from "@/components/SideBar/nav-marketPlace";
import { NavDevelopers } from "@/components/SideBar/nav-developers";
import { NavRisk } from "./nav-risk";

const data = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },

  navDataBrew: [
    {
      title: "Risk Management",
      url: "#",
      icon: Shield,
      items: [
        { title: " Screening", url: "/screening", icon: PlusCircle },
        { title: "Verification", url: "/verification", icon: CheckCircle },
        { title: "Monitoring", url: "/monitoring", icon: AlertTriangle },
      ],
    },
  ],
};

export function AppSidebar({ ...props }) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="#">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg text-sidebar-primary-foreground  w-full">
                  <Image src={"/logo.png"} width={32} height={32} alt="logo" />
                </div>
                {/* <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Leveller</span>
                  <span className="truncate text-xs">
                    ...Simplified Data Insights
                  </span>
                </div> */}
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent className="mt-2 overflow-y-scroll scrollbar-hide ">
        <NavMain />
        <NavDataBrew items={data.navDataBrew} />

        {/* <NavRisk /> */}

        {/* <NavMlAnalytics /> */}
        <NavWorkFlows />
        <NavMarketPlace />

        <NavDevelopers />
      </SidebarContent>

      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
