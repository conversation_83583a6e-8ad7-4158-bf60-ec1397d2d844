"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { motion, AnimatePresence } from 'framer-motion';
import {
    Database,
    Upload,
    Download,
    Edit,
    Trash2,
    Search,
    Filter,
    ArrowUpDown,
    Loader2,
    AlertTriangle,
    FileText,
    Calendar,
    HardDrive,
    Info,
    UserCircle,
    Tag,
    GripVertical,
} from 'lucide-react';
import { cn } from "@/lib/utils";

// Mock Data
const mockDatasets = [
    {
        id: 'd1',
        name: 'CustomerTransactions_Q2',
        description: 'Financial transactions data for Q2 2024.',
        format: 'CSV',
        size: 12.5,
        uploadedBy: 'Alice Smith',
        uploadDate: '2024-07-20',
        status: 'Available',
        tags: ['finance', 'transactions', '2024']
    },
    {
        id: 'd2',
        name: 'ProductCatalog_V3',
        description: 'Latest product catalog with new SKUs and descriptions.',
        format: 'JSON',
        size: 3.2,
        uploadedBy: 'Bob Johnson',
        uploadDate: '2024-07-25',
        status: 'Processing',
        tags: ['products', 'catalog', 'ecommerce']
    },
    {
        id: 'd3',
        name: 'WebsiteTraffic_July',
        description: 'Aggregated website traffic data for July 2024.',
        format: 'Parquet',
        size: 85.1,
        uploadedBy: 'Charlie Brown',
        uploadDate: '2024-07-30',
        status: 'Available',
        tags: ['web_analytics', 'traffic', 'july']
    },
    {
        id: 'd4',
        name: 'UserFeedback_Survey',
        description: 'Customer feedback survey responses from Q1.',
        format: 'XML',
        size: 0.8,
        uploadedBy: 'Diana Miller',
        uploadDate: '2024-06-15',
        status: 'Archived',
        tags: ['feedback', 'survey', 'customer_satisfaction']
    },
    {
        id: 'd5',
        name: 'SalesForecast_2025',
        description: 'Machine learning model predictions for 2025 sales.',
        format: 'CSV',
        size: 5.7,
        uploadedBy: 'Ethan Davis',
        uploadDate: '2024-07-28',
        status: 'Available',
        tags: ['sales', 'forecast', 'ml']
    },
    {
        id: 'd6',
        name: 'EmployeeDirectory',
        description: 'Internal employee contact and department information.',
        format: 'SQL',
        size: 1.1,
        uploadedBy: 'Fiona Green',
        uploadDate: '2024-07-10',
        status: 'Available',
        tags: ['hr', 'employees', 'internal']
    },
    {
        id: 'd7',
        name: 'ServerLogs_Daily',
        description: 'Daily server access logs for security auditing.',
        format: 'JSON',
        size: 250.0,
        uploadedBy: 'John Doe',
        uploadDate: '2024-07-29',
        status: 'Error',
        tags: ['logs', 'security', 'operations']
    },
];

const formatOptions = ['CSV', 'JSON', 'XML', 'Parquet', 'SQL'];
const statusOptions = ['Available', 'Processing', 'Archived', 'Error'];

const Main = () => {
    const [datasets, setDatasets] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [formatFilter, setFormatFilter] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [sortOption, setSortOption] = useState('uploadDate');
    const [sortDirection, setSortDirection] = useState('desc');
    const [currentPage, setCurrentPage] = useState(1);
    const [datasetsPerPage] = useState(5);
    const [isAddEditDialogOpen, setIsAddEditDialogOpen] = useState(false);
    const [editingDataset, setEditingDataset] = useState(null);
    const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false);
    const [datasetToDelete, setDatasetToDelete] = useState(null);
    const [viewingDataset, setViewingDataset] = useState(null);
    const [newDataset, setNewDataset] = useState({
        name: '',
        description: '',
        format: 'CSV',
        size: 0,
        uploadedBy: 'Current User',
        uploadDate: new Date().toISOString().split('T')[0],
        status: 'Available',
        tags: [],
    });
    const [formError, setFormError] = useState(null);
    const [theme, setTheme] = useState(() => localStorage.getItem('theme') || 'dark');

    useEffect(() => {
        const fetchData = async () => {
            setTimeout(() => {
                setDatasets(mockDatasets);
                setLoading(false);
            }, 800);
        };
        fetchData();
    }, []);

    const filteredAndSortedDatasets = useCallback(() => {
        let currentDatasets = datasets.filter(dataset => {
            const searchMatch = dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                dataset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                dataset.uploadedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
                dataset.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
            const formatMatch = !formatFilter || dataset.format === formatFilter;
            const statusMatch = !statusFilter || dataset.status === statusFilter;
            return searchMatch && formatMatch && statusMatch;
        });

        currentDatasets.sort((a, b) => {
            const direction = sortDirection === 'asc' ? 1 : -1;
            if (sortOption === 'name') {
                return direction * a.name.localeCompare(b.name);
            } else if (sortOption === 'uploadDate') {
                return direction * a.uploadDate.localeCompare(b.uploadDate);
            } else {
                return direction * (a.size - b.size);
            }
        });
        return currentDatasets;
    }, [datasets, searchTerm, formatFilter, statusFilter, sortOption, sortDirection]);

    const displayDatasets = filteredAndSortedDatasets();
    const indexOfLastDataset = currentPage * datasetsPerPage;
    const indexOfFirstDataset = indexOfLastDataset - datasetsPerPage;
    const currentDatasets = displayDatasets.slice(indexOfFirstDataset, indexOfLastDataset);
    const totalPages = Math.ceil(displayDatasets.length / datasetsPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    const handleSort = (option) => {
        setCurrentPage(1);
        if (sortOption === option) {
            setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
        } else {
            setSortOption(option);
            setSortDirection('asc');
        }
    };

    const handleUploadDatasetClick = () => {
        setEditingDataset(null);
        setNewDataset({
            name: '',
            description: '',
            format: 'CSV',
            size: 0,
            uploadedBy: 'Current User',
            uploadDate: new Date().toISOString().split('T')[0],
            status: 'Available',
            tags: [],
        });
        setFormError(null);
        setIsAddEditDialogOpen(true);
    };

    const handleEditDatasetClick = (dataset) => {
        setEditingDataset(dataset);
        setNewDataset({ ...dataset, tags: [...dataset.tags] });
        setFormError(null);
        setIsAddEditDialogOpen(true);
    };

    const handleViewDetailsClick = (dataset) => {
        setViewingDataset(dataset);
    };

    const handleSaveDataset = () => {
        if (!newDataset.name || !newDataset.description || !newDataset.format || newDataset.size === undefined || newDataset.size < 0) {
            setFormError("Please fill in all required fields (Name, Description, Format, Size).");
            return;
        }

        if (editingDataset) {
            setDatasets(prevDatasets => prevDatasets.map(d =>
                d.id === editingDataset.id ? { ...newDataset, id: editingDataset.id } : d
            ));
        } else {
            const datasetToAdd = {
                ...newDataset,
                id: crypto.randomUUID(),
                uploadedBy: newDataset.uploadedBy || 'Current User',
                uploadDate: newDataset.uploadDate || new Date().toISOString().split('T')[0],
                status: newDataset.status || 'Available',
                tags: newDataset.tags || [],
            };
            setDatasets(prevDatasets => [datasetToAdd, ...prevDatasets]);
        }
        setIsAddEditDialogOpen(false);
        setEditingDataset(null);
        setFormError(null);
    };

    const handleDeleteClick = (datasetId) => {
        setDatasetToDelete(datasetId);
        setConfirmDeleteDialogOpen(true);
    };

    const handleConfirmDelete = () => {
        if (datasetToDelete) {
            setDatasets(prevDatasets => prevDatasets.filter(d => d.id !== datasetToDelete));
            setDatasetToDelete(null);
            setConfirmDeleteDialogOpen(false);
            setViewingDataset(null);
            setCurrentPage(1);
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewDataset(prev => ({ ...prev, [name]: name === 'size' ? parseFloat(value) : value }));
    };

    const handleSelectChange = (name, value) => {
        setNewDataset(prev => ({ ...prev, [name]: value }));
    };

    const handleTagsChange = (e) => {
        setNewDataset(prev => ({ ...prev, tags: e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag !== '') }));
    };

    // Sub-Components
    const AddEditDatasetDialog = () => (
        <AlertDialog open={isAddEditDialogOpen} onOpenChange={setIsAddEditDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border max-w-xl">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-2xl font-semibold">
                        {editingDataset ? 'Edit Dataset' : 'Upload New Dataset'}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        {editingDataset ? 'Update the dataset details below.' : 'Fill in the details to upload a new dataset.'}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                    <div className="col-span-2">
                        <label htmlFor="name" className="block text-sm font-medium text-foreground">
                            Dataset Name <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="name"
                            name="name"
                            value={newDataset.name || ''}
                            onChange={handleInputChange}
                            placeholder="e.g., CustomerTransactions_Q2"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div className="col-span-2">
                        <label htmlFor="description" className="block text-sm font-medium text-foreground">
                            Description <span className="text-red-500">*</span>
                        </label>
                        <textarea
                            id="description"
                            name="description"
                            value={newDataset.description || ''}
                            onChange={handleInputChange}
                            placeholder="Provide a brief description of the dataset"
                            className="mt-1 bg-input-background border-input-border text-foreground min-h-[80px] w-full rounded-md border shadow-sm"
                        />
                    </div>
                    <div>
                        <label htmlFor="format" className="block text-sm font-medium text-foreground">
                            Format <span className="text-red-500">*</span>
                        </label>
                        <Select onValueChange={(value) => handleSelectChange('format', value)} value={newDataset.format}>
                            <SelectTrigger className="mt-1 w-full bg-input-background border-input-border text-foreground">
                                <SelectValue placeholder="Select format" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                {formatOptions.map(format => (
                                    <SelectItem key={format} value={format || "loading"} className="hover:bg-accent hover:text-accent-foreground">
                                        {format}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <label htmlFor="size" className="block text-sm font-medium text-foreground">
                            Size (MB) <span className="text-red-500">*</span>
                        </label>
                        <Input
                            id="size"
                            name="size"
                            type="number"
                            step="0.1"
                            value={newDataset.size || 0}
                            onChange={handleInputChange}
                            placeholder="e.g., 12.5"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    <div>
                        <label htmlFor="status" className="block text-sm font-medium text-foreground">
                            Status
                        </label>
                        <Select onValueChange={(value) => handleSelectChange('status', value)} value={newDataset.status}>
                            <SelectTrigger className="mt-1 w-full bg-input-background border-input-border text-foreground">
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                {statusOptions.map(stat => (
                                    <SelectItem key={stat} value={stat || "loading"} className="hover:bg-accent hover:text-accent-foreground">
                                        {stat}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                    <div>
                        <label htmlFor="tags" className="block text-sm font-medium text-foreground">
                            Tags (comma-separated)
                        </label>
                        <Input
                            id="tags"
                            name="tags"
                            value={newDataset.tags ? newDataset.tags.join(', ') : ''}
                            onChange={handleTagsChange}
                            placeholder="e.g., finance, marketing, 2024"
                            className="mt-1 bg-input-background border-input-border text-foreground"
                        />
                    </div>
                    {formError && (
                        <div className="col-span-2 text-red-500 text-sm mt-2 flex items-center gap-1">
                            <AlertTriangle className="h-4 w-4" /> {formError}
                        </div>
                    )}
                </div>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => {
                            setIsAddEditDialogOpen(false);
                            setFormError(null);
                        }}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={handleSaveDataset}
                    >
                        {editingDataset ? 'Save Changes' : 'Upload Dataset'}
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const DatasetDetailsDialog = ({ dataset }) => (
        <AlertDialog open={!!dataset} onOpenChange={() => setViewingDataset(null)}>
            <AlertDialogContent className="bg-background text-foreground border-border max-w-2xl">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-2xl font-semibold flex items-center gap-2">
                        <FileText className="h-7 w-7 text-muted-foreground" />
                        {dataset.name}
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground line-clamp-3">
                        {dataset.description}
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <div className="space-y-4 py-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <HardDrive className="h-4 w-4" />
                            <span className="font-medium text-foreground">Format:</span> {dataset.format}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Info className="h-4 w-4" />
                            <span className="font-medium text-foreground">Size:</span> {dataset.size} MB
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <UserCircle className="h-4 w-4" />
                            <span className="font-medium text-foreground">Uploaded By:</span> {dataset.uploadedBy}
                        </div>
                        <div className="flex items-center gap-2 text-muted-foreground">
                            <Calendar className="h-4 w-4" />
                            <span className="font-medium text-foreground">Upload Date:</span> {dataset.uploadDate}
                        </div>
                    </div>
                    <div>
                        <h3 className="text-lg font-semibold text-foreground mb-2">Status</h3>
                        <Badge
                            className={cn(
                                "px-3 py-1 rounded-full text-sm font-medium border-0",
                                dataset.status === 'Available' && 'bg-green-500/20 text-green-400',
                                dataset.status === 'Processing' && 'bg-blue-500/20 text-blue-400',
                                dataset.status === 'Archived' && 'bg-gray-500/20 text-gray-400',
                                dataset.status === 'Error' && 'bg-red-500/20 text-red-400'
                            )}
                        >
                            {dataset.status}
                        </Badge>
                    </div>
                    {dataset.tags && dataset.tags.length > 0 && (
                        <div>
                            <h3 className="text-lg font-semibold text-foreground mb-2">Tags</h3>
                            <div className="flex flex-wrap gap-2">
                                {dataset.tags.map((tag, index) => (
                                    <Badge key={index} variant="secondary" className="bg-secondary text-secondary-foreground">
                                        <Tag className="h-3 w-3 mr-1" /> {tag}
                                    </Badge>
                                ))}
                            </div>
                        </div>
                    )}
                </div>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <Button
                        variant="outline"
                        className="bg-primary text-primary-foreground hover:bg-primary/90"
                        onClick={() => { console.log(`Downloading ${dataset.name}`); }}
                    >
                        <Download className="h-4 w-4 mr-2" /> Download
                    </Button>
                    <Button
                        variant="outline"
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => {
                            setViewingDataset(null);
                            handleEditDatasetClick(dataset);
                        }}
                    >
                        <Edit className="h-4 w-4 mr-2" /> Edit
                    </Button>
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setViewingDataset(null)}
                    >
                        Close
                    </AlertDialogCancel>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    const ConfirmDeleteDialog = () => (
        <AlertDialog open={confirmDeleteDialogOpen} onOpenChange={setConfirmDeleteDialogOpen}>
            <AlertDialogContent className="bg-background text-foreground border-border">
                <AlertDialogHeader>
                    <AlertDialogTitle className="text-xl text-destructive flex items-center gap-2">
                        <Trash2 className="h-6 w-6" /> Confirm Deletion
                    </AlertDialogTitle>
                    <AlertDialogDescription className="text-muted-foreground">
                        Are you sure you want to delete this dataset? This action cannot be undone.
                    </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
                    <AlertDialogCancel
                        className="bg-secondary text-secondary-foreground hover:bg-secondary/80"
                        onClick={() => setConfirmDeleteDialogOpen(false)}
                    >
                        Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        onClick={handleConfirmDelete}
                    >
                        Delete
                    </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
    );

    return (
        <div className="w-full bg-background text-foreground transition-colors duration-200">
            <div className="w-full">
                <h1 className="text-3xl font-bold mb-6 flex items-center gap-3 text-foreground">
                    <GripVertical className="text-muted-foreground" />
                    Datasets
                </h1>

                {/* Toolbar: Search, Filters, Upload Dataset Button */}
                <div className="flex flex-col md:flex-row gap-4 mb-6 items-center">
                    <div className="relative w-full md:w-1/3">
                        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                            type="text"
                            placeholder="Search datasets..."
                            value={searchTerm}
                            onChange={(e) => {
                                setSearchTerm(e.target.value);
                                setCurrentPage(1);
                            }}
                            className="pl-9 bg-input-background border-input-border text-foreground focus:ring-ring focus:border-primary"
                        />
                    </div>
                    <div className="flex flex-col sm:flex-row gap-4 w-full md:w-2/3 justify-start md:justify-end">
                        <Select onValueChange={(value) => { setFormatFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={formatFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Format" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Formats</SelectItem>
                                {formatOptions.map(format => (
                                    <SelectItem key={format} value={format} className="hover:bg-accent hover:text-accent-foreground">
                                        {format}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Select onValueChange={(value) => { setStatusFilter(value === 'all' ? '' : value); setCurrentPage(1); }} value={statusFilter || 'all'}>
                            <SelectTrigger className="w-full sm:w-[180px] bg-input-background border-input-border text-foreground">
                                <Filter className="mr-2 h-4 w-4 text-muted-foreground" />
                                <SelectValue placeholder="Filter by Status" />
                            </SelectTrigger>
                            <SelectContent className="bg-popover text-popover-foreground border-border">
                                <SelectItem value="all" className="hover:bg-accent hover:text-accent-foreground">All Statuses</SelectItem>
                                {statusOptions.map(stat => (
                                    <SelectItem key={stat} value={stat} className="hover:bg-accent hover:text-accent-foreground">
                                        {stat}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        <Button
                            onClick={handleUploadDatasetClick}
                            className="bg-primary hover:bg-primary/90 text-primary-foreground w-full sm:w-auto flex-shrink-0"
                        >
                            <Upload className="mr-2 h-4 w-4" /> Upload Dataset
                        </Button>
                    </div>
                </div>

                {/* Dataset Table / Card View */}
                {loading ? (
                    <div className="flex justify-center items-center h-64 bg-card rounded-lg border border-border shadow-md">
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                    </div>
                ) : displayDatasets.length === 0 ? (
                    <div className="bg-card border border-border rounded-lg p-6 text-center shadow-md">
                        <AlertTriangle className="mx-auto h-8 w-8 mb-3 text-muted-foreground" />
                        <p className="text-lg text-muted-foreground">No datasets found matching your criteria.</p>
                        <p className="text-sm mt-1 text-muted-foreground">Try adjusting your filters or uploading a new dataset.</p>
                    </div>
                ) : (
                    <>
                        {/* Desktop Table View */}
                        <div className="hidden md:block bg-card rounded-lg border border-border overflow-x-auto shadow-md">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-muted hover:bg-muted">
                                        <TableHead onClick={() => handleSort('name')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Dataset Name {sortOption === 'name' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Format</TableHead>
                                        <TableHead onClick={() => handleSort('size')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Size (MB) {sortOption === 'size' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Uploaded By</TableHead>
                                        <TableHead onClick={() => handleSort('uploadDate')} className="cursor-pointer text-muted-foreground hover:text-foreground transition-colors">
                                            <div className="flex items-center">
                                                Upload Date {sortOption === 'uploadDate' && <ArrowUpDown className={cn("ml-2 h-4 w-4", sortDirection === 'desc' && "rotate-180")} />}
                                            </div>
                                        </TableHead>
                                        <TableHead className="text-muted-foreground">Status</TableHead>
                                        <TableHead className="text-right text-muted-foreground">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    <AnimatePresence>
                                        {currentDatasets.map(dataset => (
                                            <motion.tr
                                                key={dataset.id}
                                                initial={{ opacity: 0, y: 20 }}
                                                animate={{ opacity: 1, y: 0 }}
                                                exit={{ opacity: 0, x: -20 }}
                                                transition={{ duration: 0.2 }}
                                                className="border-border hover:bg-accent/50 transition-colors"
                                            >
                                                <TableCell className="font-medium text-foreground">{dataset.name}</TableCell>
                                                <TableCell className="text-muted-foreground">{dataset.format}</TableCell>
                                                <TableCell className="text-muted-foreground">{dataset.size.toFixed(1)}</TableCell>
                                                <TableCell className="text-muted-foreground">{dataset.uploadedBy}</TableCell>
                                                <TableCell className="text-muted-foreground">{dataset.uploadDate}</TableCell>
                                                <TableCell>
                                                    <Badge
                                                        className={cn(
                                                            "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                            dataset.status === 'Available' && 'bg-green-500/20 text-green-400',
                                                            dataset.status === 'Processing' && 'bg-blue-500/20 text-blue-400',
                                                            dataset.status === 'Archived' && 'bg-gray-500/20 text-gray-400',
                                                            dataset.status === 'Error' && 'bg-red-500/20 text-red-400'
                                                        )}
                                                    >
                                                        {dataset.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex justify-end gap-2">
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleViewDetailsClick(dataset)}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="View Details"
                                                        >
                                                            <Info className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleEditDatasetClick(dataset)}
                                                            className="text-primary hover:bg-primary/10 border-primary/20"
                                                            title="Edit Dataset"
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => handleDeleteClick(dataset.id)}
                                                            className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                                            title="Delete Dataset"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </motion.tr>
                                        ))}
                                    </AnimatePresence>
                                </TableBody>
                            </Table>
                        </div>

                        {/* Mobile Card View */}
                        <div className="md:hidden grid grid-cols-1 gap-4">
                            <AnimatePresence>
                                {currentDatasets.map(dataset => (
                                    <motion.div
                                        key={dataset.id}
                                        initial={{ opacity: 0, y: 20 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        exit={{ opacity: 0, x: -20 }}
                                        transition={{ duration: 0.2 }}
                                        className="bg-card rounded-lg border border-border p-4 shadow-md flex items-center space-x-4"
                                    >
                                        <FileText className="h-12 w-12 text-muted-foreground" />
                                        <div className="flex-1">
                                            <p className="font-semibold text-lg text-foreground">{dataset.name}</p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <HardDrive className="h-3 w-3" /> {dataset.format}
                                            </p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <Info className="h-3 w-3" /> {dataset.size.toFixed(1)} MB
                                            </p>
                                            <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                <UserCircle className="h-3 w-3" /> {dataset.uploadedBy}
                                            </p>
                                            <div className="flex items-center gap-2 mt-2">
                                                <Badge
                                                    className={cn(
                                                        "px-2 py-1 rounded-full text-xs font-medium border-0",
                                                        dataset.status === 'Available' && 'bg-green-500/20 text-green-400',
                                                        dataset.status === 'Processing' && 'bg-blue-500/20 text-blue-400',
                                                        dataset.status === 'Archived' && 'bg-gray-500/20 text-gray-400',
                                                        dataset.status === 'Error' && 'bg-red-500/20 text-red-400'
                                                    )}
                                                >
                                                    {dataset.status}
                                                </Badge>
                                            </div>
                                            <p className="text-xs text-muted-foreground mt-1 flex items-center gap-1">
                                                <Calendar className="h-3 w-3" /> Upload Date: {dataset.uploadDate}
                                            </p>
                                        </div>
                                        <div className="flex flex-col gap-2">
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleViewDetailsClick(dataset)}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Info className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleEditDatasetClick(dataset)}
                                                className="text-primary hover:bg-primary/10 border-primary/20"
                                            >
                                                <Edit className="h-4 w-4" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                onClick={() => handleDeleteClick(dataset.id)}
                                                className="text-destructive hover:bg-destructive/10 border-destructive/20"
                                            >
                                                <Trash2 className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </motion.div>
                                ))}
                            </AnimatePresence>
                        </div>
                    </>
                )}

                {/* Pagination Controls */}
                {totalPages > 1 && (
                    <div className="flex justify-center mt-6">
                        <nav className="flex items-center space-x-2">
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage - 1)}
                                disabled={currentPage === 1}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Previous
                            </Button>
                            <div className="flex space-x-1">
                                {Array.from({ length: totalPages }, (_, i) => (
                                    <Button
                                        key={i + 1}
                                        variant={currentPage === i + 1 ? "default" : "outline"}
                                        onClick={() => paginate(i + 1)}
                                        className={cn(
                                            "w-10 h-10 rounded-full",
                                            currentPage === i + 1 ? "bg-primary hover:bg-primary/90 text-primary-foreground" : "bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                                        )}
                                    >
                                        {i + 1}
                                    </Button>
                                ))}
                            </div>
                            <Button
                                variant="outline"
                                onClick={() => paginate(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className="bg-card border-border text-foreground hover:bg-accent hover:text-accent-foreground"
                            >
                                Next
                            </Button>
                        </nav>
                    </div>
                )}

                {/* Dialogs */}
                <AddEditDatasetDialog />
                <AnimatePresence>
                    {viewingDataset && (
                        <DatasetDetailsDialog dataset={viewingDataset} />
                    )}
                </AnimatePresence>
                <ConfirmDeleteDialog />
            </div>
        </div>
    );
};

export default Main;