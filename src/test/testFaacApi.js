// Test script for FAAC API integration
// This can be run in the browser console to test the API

import {
  getFaacStateData,
  getAllFaacStateData,
  transformFaacDataForCharts,
  testFaacEndpoint,
} from "../services/faacApi.js";
import { authFetch } from "../lib/authFetch.js";

// Test basic connectivity first
export async function testBasicConnectivity() {
  console.log("🔌 Testing basic API connectivity...");

  try {
    const API_BASE_URL =
      process.env.NEXT_PUBLIC_API_BASE || "http://*************:9000";

    // Test with a known working endpoint
    console.log("📡 Testing known working endpoint...");
    const response = await authFetch(
      `${API_BASE_URL}/api/playground/nodes/workflow/`,
      {
        method: "GET",
      }
    );

    const data = await response.json();
    console.log("✅ Basic connectivity successful:", data);
    return { success: true, data };
  } catch (error) {
    console.log("❌ Basic connectivity failed:", error.message);
    return { success: false, error: error.message };
  }
}

export async function testFaacApiIntegration() {
  console.log("🧪 Testing FAAC API Integration...");

  try {
    // Test basic connectivity first
    console.log("🔌 Testing basic connectivity...");
    const connectivityResult = await testBasicConnectivity();

    if (!connectivityResult.success) {
      console.log("❌ Basic connectivity failed, skipping FAAC tests");
      return;
    }

    // Test FAAC endpoint availability
    console.log("🎯 Testing FAAC endpoint availability...");
    const endpointTest = await testFaacEndpoint();
    console.log("FAAC endpoint test result:", endpointTest);

    // Test single page fetch
    console.log("📄 Testing single page fetch...");
    const singlePageResult = await getFaacStateData({ page: 1, page_size: 5 });

    if (singlePageResult.success) {
      console.log("✅ Single page fetch successful:", singlePageResult);
    } else {
      console.log("❌ Single page fetch failed:", singlePageResult.error);
    }

    // Test data transformation
    if (singlePageResult.success && singlePageResult.data) {
      console.log("🔄 Testing data transformation...");
      const transformedData = transformFaacDataForCharts(
        Array.isArray(singlePageResult.data)
          ? singlePageResult.data
          : [singlePageResult.data]
      );
      console.log("✅ Data transformation successful:", transformedData);
    }

    // Test full data fetch (commented out to avoid too many requests during testing)
    // console.log('📚 Testing full data fetch...');
    // const allDataResult = await getAllFaacStateData(10);
    // if (allDataResult.success) {
    //   console.log('✅ Full data fetch successful. Total records:', allDataResult.totalRecords);
    // } else {
    //   console.log('❌ Full data fetch failed:', allDataResult.error);
    // }
  } catch (error) {
    console.error("💥 Test failed with error:", error);
  }
}

// Test authentication store
export function testAuthStore() {
  console.log("🔐 Testing Auth Store...");

  try {
    const { useAuthStore } = require("../store/authStore.js");
    const authState = useAuthStore.getState();

    console.log("Auth state:", {
      hasToken: !!authState.accessToken,
      clientId: authState.clientId,
      clientSecret: authState.clientSecret ? "***" : "not set",
    });

    return authState;
  } catch (error) {
    console.error("❌ Auth store test failed:", error);
    return null;
  }
}

// Manual test function that can be called from browser console
window.testFaacIntegration = async function () {
  console.log("🚀 Starting FAAC Integration Tests...");

  // Test auth store first
  const authState = testAuthStore();

  if (!authState?.accessToken) {
    console.warn("⚠️ No access token found. Please log in first.");
    return;
  }

  // Test basic connectivity first
  await testBasicConnectivity();

  // Test API integration
  await testFaacApiIntegration();

  console.log("✨ Tests completed!");
};

// Also expose individual test functions
window.testBasicConnectivity = testBasicConnectivity;
window.testAuthStore = testAuthStore;
window.testFaacEndpoint = testFaacEndpoint;

console.log(
  "🔧 Test utilities loaded. Run window.testFaacIntegration() to test the integration."
);
