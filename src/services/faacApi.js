import { authFetch } from "@/lib/authFetch";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE;

// FAAC API credentials from environment variables
const FAAC_CLIENT_ID = process.env.NEXT_PUBLIC_FAAC_CLIENT_ID;
const FAAC_CLIENT_SECRET = process.env.NEXT_PUBLIC_FAAC_CLIENT_SECRET;

// Validate that credentials are loaded
if (!FAAC_CLIENT_ID || !FAAC_CLIENT_SECRET) {
  console.error("FAAC API credentials not found in environment variables:", {
    clientId: !!FAAC_CLIENT_ID,
    clientSecret: !!FAAC_CLIENT_SECRET,
  });
}

/**
 * Enhanced fetch function for FAAC API calls that includes client credentials
 * @param {string} url - The API endpoint URL
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} - The fetch response
 */
async function faacAuthFetch(url, options = {}) {
  // Add FAAC client credentials to headers
  const headers = {
    ...options.headers,
    "client-id": FAAC_CLIENT_ID,
    "client-secret": FAAC_CLIENT_SECRET,
  };

  console.log("FAAC API Request:", {
    url,
    method: options.method || "GET",
    headers: {
      ...headers,
      "client-secret": "***HIDDEN***", // Don't log the actual secret
    },
  });

  try {
    // Use the standard authFetch with additional headers
    const response = await authFetch(url, {
      ...options,
      headers,
    });

    console.log("FAAC API Response:", {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    });

    return response;
  } catch (error) {
    console.error("FAAC API Request Failed:", {
      url,
      error: error.message,
      stack: error.stack,
    });
    throw error;
  }
}

/**
 * Fetch FAAC state allocation data
 * @param {Object} params - Query parameters
 * @param {number} params.page - Page number (required)
 * @param {number} params.page_size - Page size (default: 12)
 * @returns {Promise<Object>} - The API response data
 */
/**
 * Test if the FAAC endpoint is available
 */
export async function testFaacEndpoint() {
  try {
    const url = `${API_BASE_URL}/api/databrew/faac/state/?page=1&page_size=1`;
    const response = await faacAuthFetch(url, { method: "GET" });
    return { available: true, status: response.status };
  } catch (error) {
    console.warn("FAAC endpoint not available:", error.message);
    return { available: false, error: error.message };
  }
}

export async function getFaacStateData(params = {}) {
  try {
    const { page = 1, page_size = 12 } = params;

    // Build query string
    const queryParams = new URLSearchParams({
      page: page.toString(),
      page_size: page_size.toString(),
    });

    const url = `${API_BASE_URL}/api/databrew/faac/state/?${queryParams}`;

    console.log("Attempting to fetch FAAC data from:", url);

    const response = await faacAuthFetch(url, {
      method: "GET",
    });

    const data = await response.json();
    console.log("FAAC API response:", data);

    return {
      success: true,
      data: data,
    };
  } catch (error) {
    console.error("Error fetching FAAC state data:", error);
    console.error("Full error details:", {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });

    // Try to get more details from the response if available
    let detailedError = error.message;
    if (error.response) {
      try {
        const errorText = await error.response.text();
        console.error("API Error Response:", errorText);
        detailedError = `${error.message} - Response: ${errorText}`;
      } catch (e) {
        console.error("Could not read error response:", e);
      }
    }

    // Provide more specific error messages
    let errorMessage = `Failed to fetch FAAC state data: ${detailedError}`;
    if (error.message.includes("404")) {
      errorMessage = `FAAC endpoint not found (404): ${detailedError}`;
    } else if (error.message.includes("500")) {
      errorMessage = `Server error (500): ${detailedError}`;
    } else if (error.message.includes("401") || error.message.includes("403")) {
      errorMessage = `Authentication failed (${
        error.message.includes("401") ? "401" : "403"
      }): ${detailedError}`;
    }

    return {
      success: false,
      error: errorMessage,
      originalError: error.message,
      fullError: detailedError,
    };
  }
}

/**
 * Fetch all FAAC state data with pagination
 * @param {number} page_size - Page size for each request (default: 50)
 * @returns {Promise<Object>} - All paginated data combined
 */
export async function getAllFaacStateData(page_size = 50) {
  try {
    let allData = [];
    let currentPage = 1;
    let hasMoreData = true;

    while (hasMoreData) {
      const result = await getFaacStateData({
        page: currentPage,
        page_size,
      });

      if (!result.success) {
        throw new Error(result.error);
      }

      const { data } = result;

      // Check if data has results array (common pagination pattern)
      if (data.results && Array.isArray(data.results)) {
        allData = [...allData, ...data.results];
        hasMoreData = data.next !== null; // Check if there's a next page
      } else if (Array.isArray(data)) {
        // If data is directly an array
        allData = [...allData, ...data];
        hasMoreData = data.length === page_size; // Assume no more data if less than page_size
      } else {
        // Single object response
        allData.push(data);
        hasMoreData = false;
      }

      currentPage++;

      // Safety check to prevent infinite loops
      if (currentPage > 100) {
        console.warn(
          "Reached maximum page limit (100) while fetching FAAC data"
        );
        break;
      }
    }

    return {
      success: true,
      data: allData,
      totalRecords: allData.length,
    };
  } catch (error) {
    console.error("Error fetching all FAAC state data:", error);
    return {
      success: false,
      error: error.message || "Failed to fetch all FAAC state data",
    };
  }
}

/**
 * Transform API data to match the expected chart data format
 * @param {Array} apiData - Raw API data
 * @returns {Array} - Transformed data for charts
 */
export function transformFaacDataForCharts(apiData) {
  if (!Array.isArray(apiData)) {
    console.warn(
      "Expected array for FAAC data transformation, got:",
      typeof apiData
    );
    return [];
  }

  return apiData.map((item) => ({
    // Map API fields to expected chart fields
    beneficiaries: item.state || item.beneficiaries || item.state_name,
    month: item.month,
    year: item.year,
    date: item.date,
    gross_vat_allocation: item.gross_vat_allocation || item.gross_vat || 0,
    net_vat_allocation: item.net_vat_allocation || item.net_vat || 0,
    statutory_allocation: item.statutory_allocation || 0,
    exchange_gain_allocation: item.exchange_gain_allocation || 0,
    total_net_amount: item.total_net_amount || item.net_allocation || 0,
    net_share_of_ecology: item.net_share_of_ecology || 0,
    // Include all original fields for compatibility
    ...item,
  }));
}
