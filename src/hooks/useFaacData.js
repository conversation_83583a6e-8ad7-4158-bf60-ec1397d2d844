import { useState, useEffect, useCallback } from "react";
import { getAllFaacStateData, transformFaacDataForCharts } from "@/services/faacApi";
import { useAuthStore } from "@/store/authStore";

/**
 * Custom hook for managing FAAC state data
 * @returns {Object} - Hook state and functions
 */
export function useFaacData() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [lastFetched, setLastFetched] = useState(null);
  
  const { accessToken } = useAuthStore();

  const fetchData = useCallback(async (forceRefresh = false) => {
    // Don't fetch if no access token
    if (!accessToken) {
      setError("No access token available");
      return;
    }

    // Don't fetch if data was recently fetched (unless forced)
    if (!forceRefresh && lastFetched && Date.now() - lastFetched < 5 * 60 * 1000) {
      return; // Data is less than 5 minutes old
    }

    setLoading(true);
    setError(null);

    try {
      const result = await getAllFaacStateData();
      
      if (result.success) {
        const transformedData = transformFaacDataForCharts(result.data);
        setData(transformedData);
        setLastFetched(Date.now());
        setError(null);
      } else {
        throw new Error(result.error);
      }
    } catch (err) {
      console.error("Error fetching FAAC data:", err);
      setError(err.message || "Failed to fetch FAAC data");
      // Keep existing data on error
    } finally {
      setLoading(false);
    }
  }, [accessToken, lastFetched]);

  // Auto-fetch data when hook is first used and when access token changes
  useEffect(() => {
    if (accessToken && data.length === 0) {
      fetchData();
    }
  }, [accessToken, fetchData, data.length]);

  const refetch = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  return {
    data,
    loading,
    error,
    refetch,
    lastFetched,
  };
}

/**
 * Hook specifically for chart data with filtering capabilities
 * @param {Object} filters - Filter options
 * @returns {Object} - Filtered data and state
 */
export function useFaacChartData(filters = {}) {
  const { data, loading, error, refetch, lastFetched } = useFaacData();
  const [filteredData, setFilteredData] = useState([]);

  // Apply filters to data
  useEffect(() => {
    if (!data || data.length === 0) {
      setFilteredData([]);
      return;
    }

    let filtered = [...data];

    // Apply year filter
    if (filters.years && filters.years.length > 0) {
      filtered = filtered.filter(item => filters.years.includes(item.year));
    }

    // Apply month filter
    if (filters.months && filters.months.length > 0) {
      filtered = filtered.filter(item => filters.months.includes(item.month));
    }

    // Apply state filter
    if (filters.states && filters.states.length > 0) {
      filtered = filtered.filter(item => filters.states.includes(item.beneficiaries));
    }

    setFilteredData(filtered);
  }, [data, filters]);

  return {
    data: filteredData,
    rawData: data,
    loading,
    error,
    refetch,
    lastFetched,
  };
}
