import { NextResponse } from "next/server";

export function middleware(req) {
  // Runs only for paths in `config.matcher`
  const token = req.cookies.get("access")?.value;

  // No token → bounce to sign-in
  if (!token) {
    return NextResponse.redirect(new URL("/signIn", req.url));
  }

  // Token present → allow request
  return NextResponse.next();
}

/* MUST be a literal object so Turbopack can statically analyse it */
export const config = {
  matcher: [
    // "/playground(:/.*)?",
    "/workspace(:/.*)?",
    "/dashboard(:/.*)?",
    // add more protected roots here
  ],
};
