"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

// Components
import ApiKeysList from "@/components/Developers/ApiKeys/ApiKeysList";
import ApiKeyCreateDialog from "@/components/Developers/ApiKeys/ApiKeyCreateDialog";
import ApiKeyDeleteDialog from "@/components/Developers/ApiKeys/ApiKeyDeleteDialog";
import ApiKeyRegenerateDialog from "@/components/Developers/ApiKeys/ApiKeyRegenerateDialog";

// Data
import {
  mockCredentials,
  availableServices,
  availablePermissions,
} from "@/constants/account-data";

function ApiKeysPage() {
  const [credentials, setCredentials] = useState(mockCredentials);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isRegenerateDialogOpen, setIsRegenerateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCredential, setSelectedCredential] = useState(null);

  // Handle create credential
  const handleCreateCredential = () => {
    setIsCreateDialogOpen(true);
  };

  // Handle create credential form submission
  const handleCreateCredentialSubmit = (newCredential) => {
    // Create new credential object
    const credential = {
      id: Date.now(),
      name: newCredential.name,
      service: newCredential.service,
      clientId: `client_${Math.random().toString(36).substring(2, 15)}`,
      clientSecret: `secret_${Math.random().toString(36).substring(2, 15)}`,
      type: "oauth2",
      status: "active",
      createdAt: new Date().toISOString(),
      lastUsed: null,
      permissions: newCredential.permissions,
    };

    // Add to credentials
    setCredentials([...credentials, credential]);
    setIsCreateDialogOpen(false);
  };

  // Handle regenerate credential
  const handleRegenerateCredential = (credential) => {
    setSelectedCredential(credential);
    setIsRegenerateDialogOpen(true);
  };

  // Handle regenerate credential confirmation
  const handleRegenerateCredentialConfirm = () => {
    // Update the credential with a new secret
    const updatedCredentials = credentials.map((cred) => {
      if (cred.id === selectedCredential.id) {
        return {
          ...cred,
          clientSecret: `secret_${Math.random().toString(36).substring(2, 15)}`,
        };
      }
      return cred;
    });

    setCredentials(updatedCredentials);
    setIsRegenerateDialogOpen(false);
  };

  // Handle delete credential
  const handleDeleteCredential = (credential) => {
    setSelectedCredential(credential);
    setIsDeleteDialogOpen(true);
  };

  // Handle delete credential confirmation
  const handleDeleteCredentialConfirm = () => {
    // Remove the credential
    setCredentials(
      credentials.filter((cred) => cred.id !== selectedCredential.id)
    );
    setIsDeleteDialogOpen(false);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-2 mb-8">
        <h1 className="text-3xl font-bold tracking-tight">API Keys</h1>
        <p className="text-muted-foreground">
          Manage API keys for accessing Leveller services
        </p>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>API Keys</CardTitle>
          <Button onClick={handleCreateCredential} className="gap-1">
            <Plus className="h-4 w-4" />
            Generate API Key
          </Button>
        </CardHeader>
        <CardContent>
          <ApiKeysList
            credentials={credentials}
            onRegenerateSecret={handleRegenerateCredential}
            onDeleteCredential={handleDeleteCredential}
          />
        </CardContent>
      </Card>

      {/* Create Credential Dialog */}
      <ApiKeyCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateCredentialSubmit}
        availableServices={availableServices}
        availablePermissions={availablePermissions}
      />

      {/* Regenerate Credential Dialog */}
      <ApiKeyRegenerateDialog
        open={isRegenerateDialogOpen}
        onOpenChange={setIsRegenerateDialogOpen}
        credential={selectedCredential}
        onConfirm={handleRegenerateCredentialConfirm}
      />

      {/* Delete Credential Dialog */}
      <ApiKeyDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        credential={selectedCredential}
        onConfirm={handleDeleteCredentialConfirm}
      />
    </div>
  );
}

export default ApiKeysPage;
