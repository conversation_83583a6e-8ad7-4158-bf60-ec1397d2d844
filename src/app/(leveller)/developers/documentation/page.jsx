"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Book, Code, FileText, Terminal } from "lucide-react";

// Components
import OverviewSection from "@/components/Developers/Documentation/OverviewSection";
import AuthenticationSection from "@/components/Developers/Documentation/AuthenticationSection";
import EndpointsSection from "@/components/Developers/Documentation/EndpointsSection";
import GuidesSection from "@/components/Developers/Documentation/GuidesSection";

function DocumentationPage() {
  const [activeTab, setActiveTab] = useState("overview");

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-2 mb-8">
        <h1 className="text-3xl font-bold tracking-tight">API Documentation</h1>
        <p className="text-muted-foreground">
          Learn how to integrate with Leveller's APIs and services
        </p>
      </div>

      <Card>
        <CardContent className="p-6">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-6"
          >
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <Book className="h-4 w-4" />
                <span className="hidden sm:inline">Overview</span>
              </TabsTrigger>
              <TabsTrigger
                value="authentication"
                className="flex items-center gap-2"
              >
                <Terminal className="h-4 w-4" />
                <span className="hidden sm:inline">Authentication</span>
              </TabsTrigger>
              <TabsTrigger
                value="endpoints"
                className="flex items-center gap-2"
              >
                <Code className="h-4 w-4" />
                <span className="hidden sm:inline">Endpoints</span>
              </TabsTrigger>
              <TabsTrigger value="guides" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="hidden sm:inline">Guides</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <OverviewSection />
            </TabsContent>

            <TabsContent value="authentication" className="space-y-4">
              <AuthenticationSection />
            </TabsContent>

            <TabsContent value="endpoints" className="space-y-4">
              <EndpointsSection />
            </TabsContent>

            <TabsContent value="guides" className="space-y-4">
              <GuidesSection />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

export default DocumentationPage;
