"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Components
import WebhookList from "@/components/Developers/Webhook/WebhookList";
import WebhookEvents from "@/components/Developers/Webhook/WebhookEvents";
import WebhookGuide from "@/components/Developers/Webhook/WebhookGuide";
import WebhookCreateDialog from "@/components/Developers/Webhook/WebhookCreateDialog";
import WebhookDeleteDialog from "@/components/Developers/Webhook/WebhookDeleteDialog";

// Data
import { mockWebhooks, availableWebhookEvents } from "@/constants/account-data";

function WebhookPage() {
  const [webhooks, setWebhooks] = useState(mockWebhooks);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState(null);
  const [activeTab, setActiveTab] = useState("webhooks");

  // Handle create webhook
  const handleCreateWebhook = () => {
    setIsCreateDialogOpen(true);
  };

  // Handle create webhook form submission
  const handleCreateWebhookSubmit = (newWebhook) => {
    // Create new webhook object
    const webhook = {
      id: Date.now(),
      name: newWebhook.name,
      url: newWebhook.url,
      events: newWebhook.events,
      active: newWebhook.active,
      secretKey: `whsec_${Math.random().toString(36).substring(2, 15)}`,
      createdAt: new Date().toISOString(),
      lastTriggered: null,
    };

    // Add to webhooks
    setWebhooks([...webhooks, webhook]);
    setIsCreateDialogOpen(false);
  };

  // Handle toggle webhook active state
  const handleToggleActive = (id) => {
    setWebhooks(
      webhooks.map((webhook) =>
        webhook.id === id ? { ...webhook, active: !webhook.active } : webhook
      )
    );
  };

  // Handle regenerate secret
  const handleRegenerateSecret = (id) => {
    setWebhooks(
      webhooks.map((webhook) =>
        webhook.id === id
          ? {
              ...webhook,
              secretKey: `whsec_${Math.random().toString(36).substring(2, 15)}`,
            }
          : webhook
      )
    );
  };

  // Handle delete webhook
  const handleDeleteWebhook = (webhook) => {
    setSelectedWebhook(webhook);
    setIsDeleteDialogOpen(true);
  };

  // Handle delete webhook confirmation
  const handleDeleteWebhookConfirm = () => {
    setWebhooks(
      webhooks.filter((webhook) => webhook.id !== selectedWebhook.id)
    );
    setIsDeleteDialogOpen(false);
  };

  return (
    <div className="container mx-auto p-6">
      <div className="space-y-2 mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Webhooks</h1>
        <p className="text-muted-foreground">
          Manage webhooks to receive real-time notifications about events in
          your account
        </p>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Webhooks</CardTitle>
          <Button onClick={handleCreateWebhook} className="gap-1">
            <Plus className="h-4 w-4" />
            Create Webhook
          </Button>
        </CardHeader>
        <CardContent>
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="space-y-4"
          >
            <TabsList>
              <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
              <TabsTrigger value="events">Available Events</TabsTrigger>
              <TabsTrigger value="guide">Integration Guide</TabsTrigger>
            </TabsList>

            <TabsContent value="webhooks" className="space-y-4">
              <WebhookList
                webhooks={webhooks}
                onToggleActive={handleToggleActive}
                onRegenerateSecret={handleRegenerateSecret}
                onDeleteWebhook={handleDeleteWebhook}
              />
            </TabsContent>

            <TabsContent value="events" className="space-y-4">
              <WebhookEvents events={availableWebhookEvents} />
            </TabsContent>

            <TabsContent value="guide" className="space-y-4">
              <WebhookGuide />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Create Webhook Dialog */}
      <WebhookCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSubmit={handleCreateWebhookSubmit}
        availableEvents={availableWebhookEvents}
      />

      {/* Delete Webhook Dialog */}
      <WebhookDeleteDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        webhook={selectedWebhook}
        onConfirm={handleDeleteWebhookConfirm}
      />
    </div>
  );
}

export default WebhookPage;
