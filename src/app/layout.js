import { Inria_Sans } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";

const inriaSans = Inria_Sans({
  variable: "--font-inria-sans",
  subsets: ["latin"],
  weight: ["300", "400", "700"], // Adjust weights as per your design needs
});

export const metadata = {
  title: "AI Data Tech - Revolutionizing AI & Data Technology",
  description:
    "Harness the power of cutting-edge AI and data technology to transform your business and stay ahead of the curve.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inriaSans.variable}  antialiased`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}
