import { create } from "zustand";
import { getWorkflows } from "@/utils/storage";

const useWorkflowStore = create((set) => ({
  currentWorkflowId: null,
  currentWorkflowName: "",
  workflows: [],
  visibleWorkflows: [],
  deleteId: null,

  setCurrentWorkflow: (id, name, lastModified = null) =>
    set({ currentWorkflowId: id, currentWorkflowName: name, lastModified }),

  clearCurrentWorkflow: () =>
    set({
      currentWorkflowId: null,
      currentWorkflowName: "",
      lastModified: null,
    }),

  loadWorkflows: async () => {
    try {
      const { success, data, error } = await getWorkflows();
      console.log("loadWorkflows result:", { success, data, error });
      if (success) {
        const workflows = Array.isArray(data) ? data : [];
        set({ workflows, visibleWorkflows: workflows.slice(0, 10) });
        console.log("Workflows loaded:", {
          workflows,
          visibleWorkflows: workflows.slice(0, 10),
        });
        return { success: true };
      }
      console.warn("Failed to load workflows:", error);
      return { success: false, error: error || "Failed to load workflows." };
    } catch (err) {
      console.error("Error in loadWorkflows:", err);
      return { success: false, error: "Error loading workflows." };
    }
  },

  updateWorkflows: (workflows) =>
    set((state) => {
      const newWorkflows = Array.isArray(workflows) ? workflows : [];
      console.log("Updating workflows:", {
        newWorkflows,
        visible: newWorkflows.slice(0, 10),
      });
      return {
        workflows: newWorkflows,
        visibleWorkflows: newWorkflows.slice(0, 10),
      };
    }),

  loadMoreWorkflows: () =>
    set((state) => ({
      visibleWorkflows: state.workflows.slice(
        0,
        state.visibleWorkflows.length + 10
      ),
    })),

  setDeleteId: (id) => set({ deleteId: id }),

  clearDeleteId: () => set({ deleteId: null }),

  startNewWorkflow: () =>
    set({
      currentWorkflowId: null,
      currentWorkflowName: "",
      lastModified: null,
    }),
}));

export default useWorkflowStore;
