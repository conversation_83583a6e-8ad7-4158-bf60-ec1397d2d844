"use client";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import { getEntityTypeFromPlan } from "@/lib/utils";

export const useAuthStore = create(
  persist(
    (set, get) => ({
      email: "",
      setEmail: (email) => set({ email }),

      plan: "",
      setPlan: (plan) => set({ plan }),
      getEntityType: () => getEntityTypeFromPlan(get().plan),

      accessToken: null,
      accessExp: null,

      // setTokens: ({ accessToken, accessExp }) =>
      //   set({ accessToken, accessExp }),

      setTokens: ({ accessToken, accessExp }) => {
        //console.log("Set token in Zustand", accessToken, new Date(accessExp));
        set({ accessToken, accessExp });
      },

      clearAuth: () => set({ accessToken: null, accessExp: null }),
      logout: () => {
        // 1) wipe Zustand
        set({ accessToken: null, accessExp: null });

        // 2) delete cookie (expires in the past)
        document.cookie =
          "access=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      },
      hasHydrated: false,
      setHasHydrated: (state) => set({ hasHydrated: state }),
    }),
    {
      name: "auth",
      onRehydrateStorage: () => (state) => {
        state?.setHydrated(true); // <- Manually sets hydration complete
      },
    }
  )
);
